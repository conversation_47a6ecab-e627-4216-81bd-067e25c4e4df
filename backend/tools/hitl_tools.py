"""
HITL (Human-in-the-Loop) enabled tools for travel booking system.

These tools implement confirmation steps and human input requirements
for critical booking and payment operations.
"""

from langchain_core.tools import tool
from typing import Optional, Dict, Any
from langgraph.types import interrupt
from agents.hitl import create_hitl_interrupt, process_hitl_response
from database.services import (
    get_hotel_by_id,
    get_available_rooms,
    create_hotel_booking,
    update_hotel_booking_payment,
    get_user_by_email
)
from utils.handler import ValidationException
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@tool
async def hitl_book_hotel_room(
    hotel_id: int, 
    check_in_date: str, 
    check_out_date: str,
    jumlah_tamu: int, 
    jumlah_kamar: int, 
    tipe_kamar: str,
    nama_pemesan: str = "", 
    email: str = "", 
    telepon: str = "",
    user_id: Optional[int] = None, 
    catatan: Optional[str] = None
):
    """
    Membuat pemesanan kamar hotel dengan konfirmasi HITL (Human-in-the-Loop).
    
    Tool ini akan meminta konfirmasi dari pengguna sebelum melakukan pemesanan.
    
    Args:
        hotel_id: ID hotel yang akan dipesan
        check_in_date: Tanggal check-in (YYYY-MM-DD)
        check_out_date: Tanggal check-out (YYYY-MM-DD)
        jumlah_tamu: Jumlah tamu yang akan menginap
        jumlah_kamar: Jumlah kamar yang akan dipesan
        tipe_kamar: Tipe kamar yang dipilih
        nama_pemesan: Nama pemesan
        email: Email pemesan
        telepon: Nomor telepon pemesan
        user_id: ID user (opsional)
        catatan: Catatan tambahan (opsional)
    
    Returns:
        String berisi konfirmasi pemesanan atau permintaan konfirmasi
    """
    try:
        logger.info(f"HITL Hotel Booking: Starting booking process for hotel {hotel_id}")
        
        # Validasi input dasar
        if not all([hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar, tipe_kamar]):
            raise ValidationException(
                message="Data pemesanan tidak lengkap",
                detail={"missing_fields": "hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar, tipe_kamar"}
            )
        
        # Validasi tanggal
        try:
            checkin = datetime.strptime(check_in_date, '%Y-%m-%d')
            checkout = datetime.strptime(check_out_date, '%Y-%m-%d')
            
            if checkin >= checkout:
                raise ValidationException(
                    message="Tanggal check-out harus setelah tanggal check-in",
                    detail={"check_in": check_in_date, "check_out": check_out_date}
                )
            
            nights = (checkout - checkin).days
            
        except ValueError:
            raise ValidationException(
                message="Format tanggal tidak valid. Gunakan format YYYY-MM-DD",
                detail={"check_in_date": check_in_date, "check_out_date": check_out_date}
            )
        
        # Ambil detail hotel
        hotel_data = await get_hotel_by_id(hotel_id)
        hotel_name = hotel_data.get('nama', f"Hotel ID {hotel_id}")
        
        # Periksa ketersediaan kamar
        available_rooms = await get_available_rooms(hotel_id, check_in_date, check_out_date, jumlah_tamu)
        
        selected_room = None
        for room in available_rooms:
            if room['tipe_kamar'] == tipe_kamar:
                selected_room = room
                break
        
        if not selected_room:
            return f"❌ Maaf, tipe kamar '{tipe_kamar}' tidak tersedia di {hotel_name} untuk tanggal {check_in_date} hingga {check_out_date}."
        
        # Periksa jumlah kamar yang diminta
        if jumlah_kamar > selected_room['jumlah_tersedia']:
            return f"❌ Maaf, hanya tersedia {selected_room['jumlah_tersedia']} kamar tipe {tipe_kamar} di {hotel_name}."
        
        # Hitung total harga
        total_harga = selected_room['harga'] * nights * jumlah_kamar
        
        # Siapkan data booking untuk konfirmasi
        booking_data = {
            "type": "hotel",
            "hotel_id": hotel_id,
            "hotel_name": hotel_name,
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "jumlah_tamu": jumlah_tamu,
            "jumlah_kamar": jumlah_kamar,
            "tipe_kamar": tipe_kamar,
            "total_harga": total_harga,
            "nights": nights,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "user_id": user_id,
            "catatan": catatan,
            "harga_per_malam": selected_room['harga']
        }
        
        # Buat pesan konfirmasi
        confirmation_message = f"""
🏨 **Konfirmasi Pemesanan Hotel**

📍 **Hotel**: {hotel_name}
📅 **Check-in**: {check_in_date}
📅 **Check-out**: {check_out_date}
🌙 **Lama Menginap**: {nights} malam
👥 **Jumlah Tamu**: {jumlah_tamu} orang
🏠 **Jumlah Kamar**: {jumlah_kamar} kamar
🛏️ **Tipe Kamar**: {tipe_kamar}
💰 **Harga per Malam**: Rp {selected_room['harga']:,}
💰 **Total Harga**: Rp {total_harga:,}

👤 **Data Pemesan**:
- Nama: {nama_pemesan}
- Email: {email}
- Telepon: {telepon}

Apakah Anda ingin melanjutkan pemesanan ini?
        """
        
        # Buat interrupt data
        interrupt_data = create_hitl_interrupt(
            message=confirmation_message,
            data=booking_data,
            interrupt_type="confirmation",
            options=["Ya, lanjutkan pemesanan", "Tidak, batalkan", "Ubah detail pemesanan"]
        )
        
        # Gunakan interrupt untuk menunggu konfirmasi
        user_response = interrupt(interrupt_data)
        
        # Jika user_response adalah None, berarti ini adalah interrupt pertama
        if user_response is None:
            logger.info("HITL Hotel Booking: Waiting for user confirmation")
            return confirmation_message + "\n\n⏳ Menunggu konfirmasi Anda..."
        
        # Proses respons pengguna
        logger.info(f"HITL Hotel Booking: Received user response: {user_response}")
        processed_response = process_hitl_response(user_response, "confirmation")
        
        if processed_response["action"] == "confirm":
            # Lanjutkan dengan pemesanan
            logger.info("HITL Hotel Booking: User confirmed, proceeding with booking")
            
            # Cek user_id jika belum ada
            if not user_id and email:
                existing_user = await get_user_by_email(email)
                if existing_user:
                    user_id = existing_user['id']
                    logger.info(f"Using existing user ID: {user_id}")
            
            # Buat data pemesanan final
            final_booking_data = {
                "hotel_id": hotel_id,
                "user_id": user_id,
                "nama_pemesan": nama_pemesan,
                "email": email,
                "telepon": telepon,
                "tanggal_mulai": check_in_date,
                "tanggal_akhir": check_out_date,
                "jumlah_tamu": jumlah_tamu,
                "jumlah_kamar": jumlah_kamar,
                "tipe_kamar": tipe_kamar,
                "total_harga": total_harga,
                "status": "pending",
                "metode_pembayaran": None,
                "status_pembayaran": "unpaid",
                "catatan": catatan
            }
            
            # Buat pemesanan
            result = await create_hotel_booking(final_booking_data)
            booking_id = result.get('id')
            
            success_message = f"""
✅ **Pemesanan Hotel Berhasil Dibuat!**

🎫 **ID Pemesanan**: {booking_id}
🏨 **Hotel**: {hotel_name}
📅 **Tanggal**: {check_in_date} s/d {check_out_date}
💰 **Total**: Rp {total_harga:,}
📋 **Status**: Menunggu Pembayaran

Silakan lanjutkan ke proses pembayaran untuk mengkonfirmasi pemesanan Anda.
            """
            
            return success_message
            
        elif processed_response["action"] == "cancel":
            logger.info("HITL Hotel Booking: User cancelled booking")
            return "❌ Pemesanan hotel dibatalkan sesuai permintaan Anda."
            
        else:
            logger.info("HITL Hotel Booking: User requested modification")
            return "📝 Silakan berikan detail perubahan yang Anda inginkan, atau mulai pemesanan baru dengan parameter yang berbeda."
    
    except Exception as e:
        logger.error(f"HITL Hotel Booking Error: {str(e)}")
        raise e

@tool
async def hitl_process_hotel_payment(booking_id: int, suggested_method: str = "transfer bank"):
    """
    Memproses pembayaran hotel dengan konfirmasi HITL (Human-in-the-Loop).
    
    Tool ini akan meminta konfirmasi metode pembayaran dari pengguna sebelum memproses.
    
    Args:
        booking_id: ID pemesanan hotel
        suggested_method: Metode pembayaran yang disarankan
    
    Returns:
        String berisi konfirmasi pembayaran atau permintaan konfirmasi
    """
    try:
        logger.info(f"HITL Hotel Payment: Starting payment process for booking {booking_id}")
        
        # Ambil detail pemesanan
        from database.services import get_hotel_booking_by_id
        booking_data = await get_hotel_booking_by_id(booking_id)
        
        if not booking_data:
            return f"❌ Pemesanan dengan ID {booking_id} tidak ditemukan."
        
        if booking_data.get('status_pembayaran') == 'paid':
            return f"✅ Pemesanan {booking_id} sudah dibayar sebelumnya."
        
        # Ambil detail hotel
        hotel_data = await get_hotel_by_id(booking_data['hotel_id'])
        hotel_name = hotel_data.get('nama', 'Hotel')
        
        total_amount = booking_data.get('total_harga', 0)
        
        # Buat pesan konfirmasi pembayaran
        payment_message = f"""
💳 **Konfirmasi Pembayaran Hotel**

🏨 **Hotel**: {hotel_name}
🎫 **ID Pemesanan**: {booking_id}
💰 **Total Pembayaran**: Rp {total_amount:,}

💳 **Pilih Metode Pembayaran**:
1. **Transfer Bank** - Transfer ke rekening hotel
2. **Kartu Kredit** - Pembayaran dengan kartu kredit/debit
3. **E-Wallet** - GoPay, OVO, DANA, dll

Silakan pilih metode pembayaran yang Anda inginkan.
        """
        
        # Buat interrupt data
        interrupt_data = create_hitl_interrupt(
            message=payment_message,
            data={
                "booking_id": booking_id,
                "total_amount": total_amount,
                "hotel_name": hotel_name,
                "booking_type": "hotel"
            },
            interrupt_type="selection",
            options=["Transfer Bank", "Kartu Kredit", "E-Wallet"]
        )
        
        # Gunakan interrupt untuk menunggu pilihan
        user_response = interrupt(interrupt_data)
        
        # Jika user_response adalah None, berarti ini adalah interrupt pertama
        if user_response is None:
            logger.info("HITL Hotel Payment: Waiting for payment method selection")
            return payment_message + "\n\n⏳ Menunggu pilihan metode pembayaran..."
        
        # Proses respons pengguna
        logger.info(f"HITL Hotel Payment: Received payment method: {user_response}")
        
        # Map respons ke metode pembayaran
        payment_method_map = {
            "Transfer Bank": "transfer bank",
            "Kartu Kredit": "kartu kredit",
            "E-Wallet": "e-wallet"
        }
        
        selected_method = payment_method_map.get(user_response, "transfer bank")
        
        # Proses pembayaran
        payment_data = {
            "metode_pembayaran": selected_method,
            "status_pembayaran": "paid",
            "status": "confirmed"
        }
        
        # Update pembayaran
        result = await update_hotel_booking_payment(booking_id, payment_data)
        
        success_message = f"""
✅ **Pembayaran Hotel Berhasil!**

🎫 **ID Pemesanan**: {booking_id}
🏨 **Hotel**: {hotel_name}
💳 **Metode Pembayaran**: {user_response}
💰 **Jumlah**: Rp {total_amount:,}
📋 **Status**: Terkonfirmasi

Terima kasih! Pemesanan hotel Anda telah dikonfirmasi. 
Anda akan menerima email konfirmasi segera.
        """
        
        return success_message
        
    except Exception as e:
        logger.error(f"HITL Hotel Payment Error: {str(e)}")
        raise e
