"""
Human-in-the-Loop (HITL) API endpoints for travel booking system.

This module provides REST API endpoints for managing HITL conversations,
allowing frontend applications to handle human input requirements during
the booking process.
"""

from fastapi import APIRouter, Depends, HTTPException, Header
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
from pydantic import BaseModel
from langchain_core.messages import HumanMessage
from langgraph.types import Command
from agents.graph import build_graph
from utils.handler import ValidationException, DatabaseException, log_exception
import logging
import uuid

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class HITLInitiateRequest(BaseModel):
    query: str
    user_context: Optional[Dict[str, Any]] = None

class HITLContinueRequest(BaseModel):
    response: str
    thread_id: str

class HITLStatusResponse(BaseModel):
    status: str
    thread_id: str
    waiting_for_human: bool
    hitl_data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class HITLResponse(BaseModel):
    status: str
    thread_id: str
    message: str
    waiting_for_human: bool
    hitl_data: Optional[Dict[str, Any]] = None
    dialog_state: Optional[str] = None

# Global graph variable
graph = None

async def get_graph():
    """Get or initialize the graph instance."""
    global graph
    if graph is None:
        graph = await build_graph()
        logger.info('HITL Graph initialized')
    return graph

@router.post("/hitl/initiate", response_model=HITLResponse, tags=["HITL"])
async def initiate_hitl_conversation(
    request: HITLInitiateRequest,
    thread_id: Optional[str] = Header(None, alias="X-THREAD-ID")
):
    """
    # 🚀 Mulai Percakapan HITL
    
    **Memulai percakapan baru dengan sistem Human-in-the-Loop.**
    
    *Endpoint ini memulai percakapan yang dapat dijeda untuk input manusia.*
    
    ## 📋 Request Body
    - **query**: Pertanyaan atau permintaan pengguna
    - **user_context**: Konteks pengguna (opsional)
    
    ## 🔧 Headers
    - **X-THREAD-ID**: ID thread percakapan (opsional, akan dibuat otomatis)
    
    ## ✅ Response Success (200)
    ```json
    {
        "status": "success",
        "thread_id": "550e8400-e29b-41d4-a716-************",
        "message": "Respons dari AI atau permintaan konfirmasi",
        "waiting_for_human": false,
        "hitl_data": null,
        "dialog_state": "supervisor"
    }
    ```
    
    ## 🔄 Response Waiting for Input (200)
    ```json
    {
        "status": "waiting_for_input",
        "thread_id": "550e8400-e29b-41d4-a716-************", 
        "message": "Konfirmasi pemesanan hotel...",
        "waiting_for_human": true,
        "hitl_data": {
            "type": "confirmation",
            "options": ["Ya, lanjutkan", "Tidak, batalkan"]
        }
    }
    ```
    """
    # Generate thread ID if not provided
    if not thread_id:
        thread_id = str(uuid.uuid4())
    
    logger.info(f'HITL: Initiating conversation with thread_id: {thread_id}')
    logger.info(f'HITL: Query: "{request.query}"')
    
    if not request.query:
        raise ValidationException(
            message="Query tidak boleh kosong",
            detail={"field": "query"}
        )
    
    try:
        # Get the graph instance
        current_graph = await get_graph()
        
        # Prepare input for the graph
        inputs = [HumanMessage(content=request.query)]
        state = {
            'messages': inputs,
            'user_context': request.user_context,
            'waiting_for_human': False,
            'hitl_data': None,
            'human_input': None,
            'booking_data': None
        }
        
        # Configuration for the graph
        config = {"configurable": {"thread_id": thread_id, "recursion_limit": 25}}
        
        logger.info(f"HITL: Invoking graph with state: {list(state.keys())}")
        
        # Invoke the graph
        try:
            response = await current_graph.ainvoke(input=state, config=config)
        except Exception as graph_error:
            logger.error(f"HITL: Graph error: {str(graph_error)}")
            
            # Handle specific graph errors
            if "GraphRecursionError" in str(graph_error) or "Recursion limit" in str(graph_error):
                return JSONResponse({
                    'status': 'error',
                    'thread_id': thread_id,
                    'message': 'Permintaan terlalu kompleks. Silakan coba dengan permintaan yang lebih sederhana.',
                    'waiting_for_human': False,
                    'hitl_data': None
                })
            else:
                raise graph_error
        
        logger.info('HITL: Graph execution completed')
        
        # Extract response data
        dialog_states = response.get('dialog_state', [])
        dialog_state = dialog_states[-1] if dialog_states else 'supervisor'
        
        messages = response.get('messages', [])
        message_content = messages[-1].content if messages else ''
        
        waiting_for_human = response.get('waiting_for_human', False)
        hitl_data = response.get('hitl_data')
        
        # Determine response status
        if waiting_for_human and hitl_data:
            status = "waiting_for_input"
            # Use the message from hitl_data if available
            if hitl_data.get('message'):
                message_content = hitl_data['message']
        else:
            status = "success"
        
        return JSONResponse({
            'status': status,
            'thread_id': thread_id,
            'message': message_content,
            'waiting_for_human': waiting_for_human,
            'hitl_data': hitl_data,
            'dialog_state': dialog_state
        })
        
    except Exception as e:
        log_exception(e)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "InternalServerError",
                "message": "Error dalam memproses permintaan HITL",
                "detail": {"original_error": str(e)}
            }
        )

@router.post("/hitl/continue", response_model=HITLResponse, tags=["HITL"])
async def continue_hitl_conversation(request: HITLContinueRequest):
    """
    # ▶️ Lanjutkan Percakapan HITL
    
    **Melanjutkan percakapan HITL dengan input dari pengguna.**
    
    *Endpoint ini digunakan untuk memberikan input manusia dan melanjutkan eksekusi graph.*
    
    ## 📋 Request Body
    - **response**: Respons/input dari pengguna
    - **thread_id**: ID thread percakapan yang akan dilanjutkan
    
    ## ✅ Response Success (200)
    ```json
    {
        "status": "success",
        "thread_id": "550e8400-e29b-41d4-a716-************",
        "message": "Pemesanan berhasil dikonfirmasi...",
        "waiting_for_human": false,
        "hitl_data": null,
        "dialog_state": "hotel_agent"
    }
    ```
    
    ## 🔄 Response Waiting for More Input (200)
    ```json
    {
        "status": "waiting_for_input",
        "thread_id": "550e8400-e29b-41d4-a716-************",
        "message": "Pilih metode pembayaran...",
        "waiting_for_human": true,
        "hitl_data": {
            "type": "selection",
            "options": ["Transfer Bank", "Kartu Kredit", "E-Wallet"]
        }
    }
    ```
    """
    logger.info(f'HITL: Continuing conversation for thread_id: {request.thread_id}')
    logger.info(f'HITL: User response: "{request.response}"')
    
    if not request.response:
        raise ValidationException(
            message="Response tidak boleh kosong",
            detail={"field": "response"}
        )
    
    if not request.thread_id:
        raise ValidationException(
            message="Thread ID tidak boleh kosong",
            detail={"field": "thread_id"}
        )
    
    try:
        # Get the graph instance
        current_graph = await get_graph()
        
        # Configuration for the graph
        config = {"configurable": {"thread_id": request.thread_id, "recursion_limit": 25}}
        
        logger.info(f"HITL: Resuming graph with user input: {request.response}")
        
        # Resume the graph with user input using Command
        try:
            response = await current_graph.ainvoke(
                Command(resume=request.response),
                config=config
            )
        except Exception as graph_error:
            logger.error(f"HITL: Graph resume error: {str(graph_error)}")
            
            # Handle specific graph errors
            if "GraphRecursionError" in str(graph_error) or "Recursion limit" in str(graph_error):
                return JSONResponse({
                    'status': 'error',
                    'thread_id': request.thread_id,
                    'message': 'Permintaan terlalu kompleks. Silakan coba dengan permintaan yang lebih sederhana.',
                    'waiting_for_human': False,
                    'hitl_data': None
                })
            else:
                raise graph_error
        
        logger.info('HITL: Graph resume completed')
        
        # Extract response data
        dialog_states = response.get('dialog_state', [])
        dialog_state = dialog_states[-1] if dialog_states else 'supervisor'
        
        messages = response.get('messages', [])
        message_content = messages[-1].content if messages else ''
        
        waiting_for_human = response.get('waiting_for_human', False)
        hitl_data = response.get('hitl_data')
        
        # Determine response status
        if waiting_for_human and hitl_data:
            status = "waiting_for_input"
            # Use the message from hitl_data if available
            if hitl_data.get('message'):
                message_content = hitl_data['message']
        else:
            status = "success"
        
        return JSONResponse({
            'status': status,
            'thread_id': request.thread_id,
            'message': message_content,
            'waiting_for_human': waiting_for_human,
            'hitl_data': hitl_data,
            'dialog_state': dialog_state
        })
        
    except Exception as e:
        log_exception(e)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "InternalServerError", 
                "message": "Error dalam melanjutkan percakapan HITL",
                "detail": {"original_error": str(e)}
            }
        )

@router.get("/hitl/status/{thread_id}", response_model=HITLStatusResponse, tags=["HITL"])
async def get_hitl_status(thread_id: str):
    """
    # 📊 Status Percakapan HITL
    
    **Mengecek status percakapan HITL apakah sedang menunggu input.**
    
    *Endpoint ini berguna untuk polling status percakapan.*
    
    ## 🔧 Path Parameters
    - **thread_id**: ID thread percakapan yang akan dicek
    
    ## ✅ Response Success (200)
    ```json
    {
        "status": "active",
        "thread_id": "550e8400-e29b-41d4-a716-************",
        "waiting_for_human": true,
        "hitl_data": {
            "type": "confirmation",
            "message": "Konfirmasi pemesanan...",
            "options": ["Ya", "Tidak"]
        },
        "message": "Menunggu konfirmasi pengguna"
    }
    ```
    """
    logger.info(f'HITL: Checking status for thread_id: {thread_id}')
    
    try:
        # Get the graph instance
        current_graph = await get_graph()
        
        # Configuration for the graph
        config = {"configurable": {"thread_id": thread_id}}
        
        # Get current state
        state = current_graph.get_state(config)
        
        if not state or not state.values:
            return JSONResponse({
                'status': 'not_found',
                'thread_id': thread_id,
                'waiting_for_human': False,
                'hitl_data': None,
                'message': 'Thread tidak ditemukan'
            })
        
        # Extract state data
        waiting_for_human = state.values.get('waiting_for_human', False)
        hitl_data = state.values.get('hitl_data')
        
        # Check if there are any interrupts
        has_interrupts = bool(state.tasks and any(task.interrupts for task in state.tasks))
        
        if has_interrupts or waiting_for_human:
            status = "waiting_for_input"
            message = "Menunggu input dari pengguna"
            
            # Try to get interrupt data
            if state.tasks:
                for task in state.tasks:
                    if task.interrupts:
                        interrupt_data = task.interrupts[0].value
                        if interrupt_data:
                            hitl_data = interrupt_data
                            if interrupt_data.get('message'):
                                message = interrupt_data['message']
                        break
        else:
            status = "active"
            message = "Percakapan aktif"
        
        return JSONResponse({
            'status': status,
            'thread_id': thread_id,
            'waiting_for_human': waiting_for_human or has_interrupts,
            'hitl_data': hitl_data,
            'message': message
        })
        
    except Exception as e:
        log_exception(e)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "InternalServerError",
                "message": "Error dalam mengecek status HITL",
                "detail": {"original_error": str(e)}
            }
        )
