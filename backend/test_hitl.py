#!/usr/bin/env python3
"""
Test script untuk HITL (Human-in-the-Loop) functionality.

Script ini menguji implementasi HITL untuk booking hotel dan pembayaran.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

API_BASE_URL = os.getenv("API_URL", "http://localhost:8000/api/v1")

async def test_hitl_hotel_booking():
    """Test HITL hotel booking flow."""
    print("🧪 Testing HITL Hotel Booking Flow")
    print("=" * 50)
    
    # Test data
    test_query = "Saya ingin pesan hotel di Ubud untuk tanggal 25 Juni 2024, 2 orang, 1 kamar"
    test_user_context = {
        "user_id": 1,
        "nama": "Test User",
        "email": "<EMAIL>",
        "telepon": "081234567890",
        "is_verified": True
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # Step 1: Initiate HITL conversation
            print("📤 Step 1: Initiating HITL conversation...")
            response = await client.post(
                f"{API_BASE_URL}/hitl/initiate",
                json={
                    "query": test_query,
                    "user_context": test_user_context
                },
                headers={"X-THREAD-ID": "test-thread-123"},
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get("status") == "waiting_for_input":
                print("\n✅ HITL interrupt triggered successfully!")
                print(f"Thread ID: {result.get('thread_id')}")
                print(f"Waiting for human: {result.get('waiting_for_human')}")
                print(f"Message: {result.get('message')}")
                
                # Step 2: Continue with user confirmation
                print("\n📤 Step 2: Continuing with user confirmation...")
                continue_response = await client.post(
                    f"{API_BASE_URL}/hitl/continue",
                    json={
                        "response": "Ya, lanjutkan pemesanan",
                        "thread_id": result.get("thread_id")
                    },
                    timeout=30.0
                )
                
                print(f"Status Code: {continue_response.status_code}")
                continue_result = continue_response.json()
                print(f"Response: {json.dumps(continue_result, indent=2)}")
                
                if continue_result.get("status") == "waiting_for_input":
                    print("\n✅ Payment confirmation triggered!")
                    
                    # Step 3: Select payment method
                    print("\n📤 Step 3: Selecting payment method...")
                    payment_response = await client.post(
                        f"{API_BASE_URL}/hitl/continue",
                        json={
                            "response": "Kartu Kredit",
                            "thread_id": result.get("thread_id")
                        },
                        timeout=30.0
                    )
                    
                    print(f"Status Code: {payment_response.status_code}")
                    payment_result = payment_response.json()
                    print(f"Response: {json.dumps(payment_result, indent=2)}")
                    
                    if payment_result.get("status") == "success":
                        print("\n🎉 HITL flow completed successfully!")
                    else:
                        print(f"\n❌ Payment step failed: {payment_result.get('message')}")
                else:
                    print(f"\n❌ Booking confirmation failed: {continue_result.get('message')}")
            else:
                print(f"\n❌ HITL not triggered: {result.get('message')}")
                
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP Error: {e.response.status_code}")
            print(f"Response: {e.response.text}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def test_hitl_status():
    """Test HITL status endpoint."""
    print("\n🧪 Testing HITL Status Check")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{API_BASE_URL}/hitl/status/test-thread-123",
                timeout=10.0
            )
            
            print(f"Status Code: {response.status_code}")
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP Error: {e.response.status_code}")
            print(f"Response: {e.response.text}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def test_regular_api():
    """Test regular API for comparison."""
    print("\n🧪 Testing Regular API (Non-HITL)")
    print("=" * 50)
    
    test_query = "Halo, apa kabar?"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{API_BASE_URL}/response/",
                json={"query": test_query},
                headers={"X-THREAD-ID": "test-thread-regular"},
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP Error: {e.response.status_code}")
            print(f"Response: {e.response.text}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def main():
    """Main test function."""
    print("🚀 HITL Testing Suite")
    print("=" * 50)
    print(f"API Base URL: {API_BASE_URL}")
    print()
    
    # Test regular API first
    await test_regular_api()
    
    # Test HITL functionality
    await test_hitl_hotel_booking()
    
    # Test status check
    await test_hitl_status()
    
    print("\n✅ Testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
