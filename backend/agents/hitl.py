"""
Human-in-the-Loop (HITL) helper functions for LangGraph agents.

This module provides utilities for implementing HITL functionality in the travel booking system,
allowing agents to pause execution and wait for human input before proceeding.
"""

from typing import Dict, Any, Optional
from langgraph.types import interrupt
from agents.state import State
import logging

logger = logging.getLogger(__name__)

def create_hitl_interrupt(
    message: str,
    data: Dict[str, Any],
    interrupt_type: str = "confirmation",
    options: Optional[list] = None
) -> Dict[str, Any]:
    """
    Create a standardized HITL interrupt with user-friendly data.
    
    Args:
        message: Message to display to the user
        data: Data context for the interrupt
        interrupt_type: Type of interrupt (confirmation, selection, input)
        options: Available options for selection type interrupts
        
    Returns:
        Dict containing interrupt data
    """
    interrupt_data = {
        "type": interrupt_type,
        "message": message,
        "data": data,
        "timestamp": None  # Will be set by the system
    }
    
    if options:
        interrupt_data["options"] = options
        
    return interrupt_data

def hitl_booking_confirmation(state: State) -> Dict[str, Any]:
    """
    HITL node for booking confirmation.
    Pauses execution to get human confirmation for booking details.
    """
    logger.info("HITL: Requesting booking confirmation from user")
    
    booking_data = state.get("booking_data", {})
    
    if not booking_data:
        logger.error("HITL: No booking data found in state")
        return {
            "messages": [{"role": "assistant", "content": "Error: No booking data found."}],
            "waiting_for_human": False
        }
    
    # Create user-friendly booking summary
    booking_type = booking_data.get("type", "unknown")
    
    if booking_type == "hotel":
        summary = f"""
🏨 **Konfirmasi Pemesanan Hotel**

📍 **Hotel**: {booking_data.get('hotel_name', 'N/A')}
📅 **Check-in**: {booking_data.get('check_in_date', 'N/A')}
📅 **Check-out**: {booking_data.get('check_out_date', 'N/A')}
👥 **Jumlah Tamu**: {booking_data.get('jumlah_tamu', 'N/A')} orang
🏠 **Jumlah Kamar**: {booking_data.get('jumlah_kamar', 'N/A')} kamar
🛏️ **Tipe Kamar**: {booking_data.get('tipe_kamar', 'N/A')}
💰 **Total Harga**: Rp {booking_data.get('total_harga', 0):,}

👤 **Data Pemesan**:
- Nama: {booking_data.get('nama_pemesan', 'N/A')}
- Email: {booking_data.get('email', 'N/A')}
- Telepon: {booking_data.get('telepon', 'N/A')}

Apakah Anda ingin melanjutkan pemesanan ini?
        """
    elif booking_type == "flight":
        summary = f"""
✈️ **Konfirmasi Pemesanan Penerbangan**

🛫 **Rute**: {booking_data.get('departure_city', 'N/A')} → {booking_data.get('arrival_city', 'N/A')}
📅 **Tanggal**: {booking_data.get('departure_date', 'N/A')}
🕐 **Waktu**: {booking_data.get('departure_time', 'N/A')}
👥 **Jumlah Penumpang**: {booking_data.get('jumlah_penumpang', 'N/A')} orang
💰 **Total Harga**: Rp {booking_data.get('total_harga', 0):,}

👤 **Data Pemesan**:
- Nama: {booking_data.get('nama_pemesan', 'N/A')}
- Email: {booking_data.get('email', 'N/A')}
- Telepon: {booking_data.get('telepon', 'N/A')}

Apakah Anda ingin melanjutkan pemesanan ini?
        """
    elif booking_type == "tour":
        summary = f"""
🏝️ **Konfirmasi Pemesanan Tour**

🎯 **Paket Tour**: {booking_data.get('tour_name', 'N/A')}
📍 **Destinasi**: {booking_data.get('destination', 'N/A')}
📅 **Tanggal**: {booking_data.get('tour_date', 'N/A')}
👥 **Jumlah Peserta**: {booking_data.get('jumlah_peserta', 'N/A')} orang
💰 **Total Harga**: Rp {booking_data.get('total_harga', 0):,}

👤 **Data Pemesan**:
- Nama: {booking_data.get('nama_pemesan', 'N/A')}
- Email: {booking_data.get('email', 'N/A')}
- Telepon: {booking_data.get('telepon', 'N/A')}

Apakah Anda ingin melanjutkan pemesanan ini?
        """
    else:
        summary = f"Konfirmasi pemesanan untuk {booking_type}"
    
    # Create interrupt data
    interrupt_data = create_hitl_interrupt(
        message=summary,
        data=booking_data,
        interrupt_type="confirmation",
        options=["Ya, lanjutkan", "Tidak, batalkan", "Ubah detail"]
    )
    
    # Use LangGraph's interrupt function
    user_response = interrupt(interrupt_data)
    
    # This will be None during the interrupt, and contain user input when resumed
    if user_response is None:
        # First time - set up interrupt state
        return {
            "hitl_data": interrupt_data,
            "waiting_for_human": True,
            "messages": [{"role": "assistant", "content": summary}]
        }
    else:
        # Resumed with user input
        logger.info(f"HITL: Received user response: {user_response}")
        return {
            "human_input": user_response,
            "waiting_for_human": False,
            "hitl_data": None
        }

def hitl_payment_confirmation(state: State) -> Dict[str, Any]:
    """
    HITL node for payment confirmation.
    Pauses execution to get human confirmation for payment details.
    """
    logger.info("HITL: Requesting payment confirmation from user")
    
    booking_data = state.get("booking_data", {})
    
    if not booking_data:
        logger.error("HITL: No booking data found in state")
        return {
            "messages": [{"role": "assistant", "content": "Error: No booking data found."}],
            "waiting_for_human": False
        }
    
    total_amount = booking_data.get('total_harga', 0)
    booking_type = booking_data.get('type', 'unknown')
    
    summary = f"""
💳 **Konfirmasi Pembayaran**

📋 **Detail Pembayaran**:
- Jenis Pemesanan: {booking_type.title()}
- Total yang harus dibayar: **Rp {total_amount:,}**

💰 **Pilih Metode Pembayaran**:
1. Transfer Bank
2. Kartu Kredit  
3. E-Wallet (GoPay, OVO, DANA)

Silakan pilih metode pembayaran yang Anda inginkan.
    """
    
    # Create interrupt data
    interrupt_data = create_hitl_interrupt(
        message=summary,
        data={"total_amount": total_amount, "booking_type": booking_type},
        interrupt_type="selection",
        options=["Transfer Bank", "Kartu Kredit", "E-Wallet"]
    )
    
    # Use LangGraph's interrupt function
    user_response = interrupt(interrupt_data)
    
    # This will be None during the interrupt, and contain user input when resumed
    if user_response is None:
        # First time - set up interrupt state
        return {
            "hitl_data": interrupt_data,
            "waiting_for_human": True,
            "messages": [{"role": "assistant", "content": summary}]
        }
    else:
        # Resumed with user input
        logger.info(f"HITL: Received payment method: {user_response}")
        
        # Map user response to payment method
        payment_method_map = {
            "Transfer Bank": "transfer bank",
            "Kartu Kredit": "kartu kredit", 
            "E-Wallet": "e-wallet"
        }
        
        selected_method = payment_method_map.get(user_response, "transfer bank")
        
        return {
            "human_input": user_response,
            "waiting_for_human": False,
            "hitl_data": None,
            "booking_data": {**booking_data, "payment_method": selected_method}
        }

def process_hitl_response(user_input: str, interrupt_type: str) -> Dict[str, Any]:
    """
    Process human input based on interrupt type.
    
    Args:
        user_input: The human input received
        interrupt_type: Type of interrupt that was waiting for input
        
    Returns:
        Dict containing processed response data
    """
    logger.info(f"Processing HITL response: {user_input} for type: {interrupt_type}")
    
    if interrupt_type == "confirmation":
        # Process confirmation responses
        positive_responses = ["ya", "yes", "lanjutkan", "setuju", "ok", "oke"]
        negative_responses = ["tidak", "no", "batal", "cancel", "batalkan"]
        
        user_lower = user_input.lower()
        
        if any(pos in user_lower for pos in positive_responses):
            return {"action": "confirm", "response": user_input}
        elif any(neg in user_lower for neg in negative_responses):
            return {"action": "cancel", "response": user_input}
        else:
            return {"action": "modify", "response": user_input}
            
    elif interrupt_type == "selection":
        # Process selection responses
        return {"action": "select", "response": user_input}
        
    elif interrupt_type == "input":
        # Process free text input
        return {"action": "input", "response": user_input}
        
    else:
        return {"action": "unknown", "response": user_input}
