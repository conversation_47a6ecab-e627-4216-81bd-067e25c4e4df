from typing import Annotated, Literal, Optional, Dict, Any
from typing_extensions import TypedDict
from langgraph.graph.message import AnyMessage, add_messages

def update_dialog_stack(left: list[str], right: Optional[str]) -> list[str]:
    """Push atau pop status dialog."""
    if right is None:
        return left
    if right == "pop":
        return left[:-1]
    return left + [right]

def update_hitl_data(left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Update HITL (Human-in-the-Loop) data."""
    if right is None:
        return left
    if left is None:
        return right
    # Merge dictionaries, with right taking precedence
    return {**left, **right}

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    dialog_state: Annotated[
        list[
            Literal[
                "supervisor",
                "customer_service",
                "hotel_agent",
                "flight_agent",
                "tour_agent",
                "hotel_booking_confirmation",
                "hotel_payment_confirmation",
                "flight_booking_confirmation",
                "flight_payment_confirmation",
                "tour_booking_confirmation",
                "tour_payment_confirmation",
            ]
        ],
        update_dialog_stack,
    ]
    user_context: Optional[Dict[str, Any]]
    # HITL (Human-in-the-Loop) fields
    hitl_data: Annotated[Optional[Dict[str, Any]], update_hitl_data]  # Data to show to human
    waiting_for_human: Optional[bool]  # Flag indicating if waiting for human input
    human_input: Optional[str]  # Human input received
    booking_data: Optional[Dict[str, Any]]  # Temporary booking data during HITL flow