"""
HITL (Human-in-the-Loop) handler for Telegram bot.

This module handles HITL interactions including booking confirmations,
payment method selections, and other user confirmations through Telegram UI.
"""

import logging
import httpx
import time
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from utils.metrics import TELEGRAM_API_CALLS, TELEGRAM_RESPONSE_TIME

logger = logging.getLogger(__name__)

class HITLHandler:
    def __init__(self, bot, api_url):
        self.bot = bot
        self.api_url = api_url
        # Store active HITL sessions
        self.active_sessions = {}
    
    async def make_hitl_api_call(self, endpoint: str, payload: dict = None, method: str = "POST") -> dict:
        """Make API call to HITL endpoints."""
        start_time = time.time()
        try:
            async with httpx.AsyncClient() as client:
                if method == "POST":
                    response = await client.post(
                        f"{self.api_url.replace('/response/', '')}/hitl/{endpoint}",
                        json=payload,
                        timeout=60.0
                    )
                elif method == "GET":
                    response = await client.get(
                        f"{self.api_url.replace('/response/', '')}/hitl/{endpoint}",
                        timeout=60.0
                    )
                
                response.raise_for_status()
                TELEGRAM_API_CALLS.labels(status="success").inc()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HITL API error: {e.response.status_code}")
            TELEGRAM_API_CALLS.labels(status="error").inc()
            return {"status": "error", "message": "API error"}
        except Exception as e:
            logger.error(f"HITL API exception: {str(e)}")
            TELEGRAM_API_CALLS.labels(status="error").inc()
            return {"status": "error", "message": str(e)}
        finally:
            response_time = time.time() - start_time
            TELEGRAM_RESPONSE_TIME.labels(message_type="hitl_api").observe(response_time)
    
    async def initiate_hitl_conversation(self, chat_id: int, user_id: int, query: str, user_context: dict = None, thread_id: str = None) -> dict:
        """Initiate HITL conversation."""
        logger.info(f"HITL: Initiating conversation for user {user_id}")
        
        payload = {
            "query": query,
            "user_context": user_context
        }
        
        headers = {}
        if thread_id:
            headers["X-THREAD-ID"] = thread_id
        
        # Make API call to initiate HITL
        result = await self.make_hitl_api_call("initiate", payload)
        
        if result.get("status") == "waiting_for_input":
            # Store session info
            session_info = {
                "chat_id": chat_id,
                "user_id": user_id,
                "thread_id": result.get("thread_id"),
                "hitl_data": result.get("hitl_data"),
                "message": result.get("message")
            }
            self.active_sessions[user_id] = session_info
            
            # Send confirmation UI
            await self.send_confirmation_ui(chat_id, result)
        else:
            # Regular response, send normally
            await self.bot.send_message(
                chat_id=chat_id,
                text=result.get("message", "Respons dari sistem"),
                parse_mode="Markdown"
            )
        
        return result
    
    async def continue_hitl_conversation(self, user_id: int, user_response: str) -> dict:
        """Continue HITL conversation with user response."""
        logger.info(f"HITL: Continuing conversation for user {user_id} with response: {user_response}")
        
        session = self.active_sessions.get(user_id)
        if not session:
            return {"status": "error", "message": "No active HITL session"}
        
        payload = {
            "response": user_response,
            "thread_id": session["thread_id"]
        }
        
        # Make API call to continue HITL
        result = await self.make_hitl_api_call("continue", payload)
        
        if result.get("status") == "waiting_for_input":
            # Update session info
            session["hitl_data"] = result.get("hitl_data")
            session["message"] = result.get("message")
            
            # Send next confirmation UI
            await self.send_confirmation_ui(session["chat_id"], result)
        else:
            # Conversation completed, clean up session
            if user_id in self.active_sessions:
                del self.active_sessions[user_id]
            
            # Send final response
            await self.bot.send_message(
                chat_id=session["chat_id"],
                text=result.get("message", "Proses selesai"),
                parse_mode="Markdown"
            )
        
        return result
    
    async def send_confirmation_ui(self, chat_id: int, hitl_response: dict):
        """Send confirmation UI based on HITL data."""
        hitl_data = hitl_response.get("hitl_data", {})
        message = hitl_response.get("message", "Konfirmasi diperlukan")
        interrupt_type = hitl_data.get("type", "confirmation")
        options = hitl_data.get("options", [])
        
        if interrupt_type == "confirmation":
            # Booking confirmation with Yes/No buttons
            keyboard = [
                [
                    InlineKeyboardButton("✅ Ya, Lanjutkan", callback_data="hitl_confirm_yes"),
                    InlineKeyboardButton("❌ Tidak, Batalkan", callback_data="hitl_confirm_no")
                ],
                [
                    InlineKeyboardButton("📝 Ubah Detail", callback_data="hitl_confirm_modify")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await self.bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode="Markdown",
                reply_markup=reply_markup
            )
            
        elif interrupt_type == "selection":
            # Payment method selection or other options
            keyboard = []
            
            # Create buttons for each option
            for i, option in enumerate(options):
                callback_data = f"hitl_select_{i}_{option.replace(' ', '_').lower()}"
                keyboard.append([InlineKeyboardButton(f"💳 {option}", callback_data=callback_data)])
            
            # Add cancel button
            keyboard.append([InlineKeyboardButton("❌ Batal", callback_data="hitl_select_cancel")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await self.bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode="Markdown",
                reply_markup=reply_markup
            )
        
        else:
            # Default text input request
            await self.bot.send_message(
                chat_id=chat_id,
                text=f"{message}\n\n💬 Silakan ketik respons Anda:",
                parse_mode="Markdown"
            )
    
    async def handle_callback_query(self, callback_query):
        """Handle callback queries from HITL UI."""
        try:
            chat_id = callback_query.message.chat.id
            user_id = callback_query.from_user.id
            callback_data = callback_query.data
            
            logger.info(f"HITL: Handling callback {callback_data} for user {user_id}")
            
            # Check if user has active HITL session
            if user_id not in self.active_sessions:
                await self.bot.answer_callback_query(
                    callback_query.id,
                    text="❌ Sesi konfirmasi sudah berakhir. Silakan mulai lagi.",
                    show_alert=True
                )
                return
            
            user_response = None
            
            # Parse callback data
            if callback_data == "hitl_confirm_yes":
                user_response = "Ya, lanjutkan pemesanan"
            elif callback_data == "hitl_confirm_no":
                user_response = "Tidak, batalkan pemesanan"
            elif callback_data == "hitl_confirm_modify":
                user_response = "Ubah detail pemesanan"
            elif callback_data.startswith("hitl_select_"):
                # Parse selection callback
                parts = callback_data.split("_")
                if len(parts) >= 3:
                    if parts[2] == "cancel":
                        user_response = "Batal"
                    else:
                        # Reconstruct option name
                        option_parts = parts[3:]  # Skip "hitl_select_index"
                        user_response = " ".join(part.replace("_", " ").title() for part in option_parts)
            
            if user_response:
                # Edit message to show user's choice
                try:
                    await self.bot.edit_message_text(
                        chat_id=chat_id,
                        message_id=callback_query.message.message_id,
                        text=f"✅ Pilihan Anda: **{user_response}**\n\n⏳ Memproses...",
                        parse_mode="Markdown"
                    )
                except Exception as e:
                    logger.warning(f"Failed to edit message: {e}")
                
                # Continue HITL conversation
                await self.continue_hitl_conversation(user_id, user_response)
            
            # Answer callback query
            await self.bot.answer_callback_query(callback_query.id)
            
        except Exception as e:
            logger.error(f"Error handling HITL callback: {e}")
            try:
                await self.bot.answer_callback_query(
                    callback_query.id,
                    text="❌ Terjadi kesalahan. Silakan coba lagi.",
                    show_alert=True
                )
            except:
                pass
    
    def is_user_in_hitl_session(self, user_id: int) -> bool:
        """Check if user has active HITL session."""
        return user_id in self.active_sessions
    
    async def handle_text_input_during_hitl(self, chat_id: int, user_id: int, text: str) -> bool:
        """Handle text input during HITL session."""
        if user_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[user_id]
        hitl_data = session.get("hitl_data", {})
        
        # If expecting text input (not button response)
        if hitl_data.get("type") == "input":
            await self.continue_hitl_conversation(user_id, text)
            return True
        
        return False
    
    async def cancel_hitl_session(self, user_id: int):
        """Cancel active HITL session."""
        if user_id in self.active_sessions:
            session = self.active_sessions[user_id]
            del self.active_sessions[user_id]
            
            await self.bot.send_message(
                chat_id=session["chat_id"],
                text="❌ Sesi konfirmasi dibatalkan.",
                parse_mode="Markdown"
            )
    
    async def get_hitl_status(self, thread_id: str) -> dict:
        """Get HITL status for a thread."""
        return await self.make_hitl_api_call(f"status/{thread_id}", method="GET")
