# HITL (Human-in-the-Loop) Testing Guide

## Overview

Panduan ini menjelaskan cara menguji implementasi HITL untuk sistem booking hotel dengan konfirmasi pengguna melalui Telegram bot.

## Fitur HITL yang Diimplementasikan

### 1. Hotel Booking Confirmation
- ✅ Konfirmasi detail pemesanan hotel
- ✅ UI tombol konfirmasi di Telegram
- ✅ Validasi input pengguna
- ✅ Integrasi dengan database

### 2. Payment Method Selection
- ✅ Pilihan metode pembayaran
- ✅ UI tombol pilihan di Telegram
- ✅ Proses pembayaran otomatis
- ✅ Konfirmasi pembayaran

## Cara Testing

### A. Testing melalui Telegram Bot

#### 1. Setup Environment
```bash
# Pastikan backend dan telegram bot berjalan
cd backend && python main.py
cd frontend/telegram && python bot.py
```

#### 2. Login ke Telegram Bot
```
/start
/login
# Masukkan email dan password
```

#### 3. Test Hotel Booking dengan HITL

**Con<PERSON>h Pesan yang Memicu HITL:**
```
<PERSON>a ingin pesan hotel di Ubud untuk tanggal 25 Juni 2024, 2 orang, 1 kamar
```

**Expected Flow:**
1. Bot akan menampilkan detail pemesanan
2. Muncul tombol konfirmasi:
   - ✅ Ya, Lanjutkan
   - ❌ Tidak, Batalkan  
   - 📝 Ubah Detail

3. Setelah konfirmasi, muncul pilihan pembayaran:
   - 💳 Transfer Bank
   - 💳 Kartu Kredit
   - 💳 E-Wallet

4. Setelah pilih pembayaran, proses selesai dengan konfirmasi

#### 4. Test Cases

**Test Case 1: Booking Berhasil**
```
Input: "Pesan hotel di Bali tanggal 20 Juni 2024, 2 orang"
Expected: HITL confirmation UI muncul
Action: Klik "Ya, Lanjutkan" → Pilih "Kartu Kredit"
Expected: Booking berhasil dengan konfirmasi
```

**Test Case 2: Booking Dibatalkan**
```
Input: "Book hotel di Jakarta tanggal 15 Juli 2024"
Expected: HITL confirmation UI muncul
Action: Klik "Tidak, Batalkan"
Expected: Booking dibatalkan
```

**Test Case 3: Ubah Detail**
```
Input: "Reservasi hotel di Yogyakarta tanggal 10 Agustus 2024"
Expected: HITL confirmation UI muncul
Action: Klik "Ubah Detail"
Expected: Kembali ke agent untuk modifikasi
```

### B. Testing melalui API Langsung

#### 1. Test HITL Initiate
```bash
curl -X POST "http://localhost:8000/api/v1/hitl/initiate" \
  -H "Content-Type: application/json" \
  -H "X-THREAD-ID: test-123" \
  -d '{
    "query": "Pesan hotel di Ubud tanggal 25 Juni 2024, 2 orang",
    "user_context": {
      "user_id": 1,
      "nama": "Test User",
      "email": "<EMAIL>",
      "telepon": "081234567890"
    }
  }'
```

#### 2. Test HITL Continue
```bash
curl -X POST "http://localhost:8000/api/v1/hitl/continue" \
  -H "Content-Type: application/json" \
  -d '{
    "response": "Ya, lanjutkan pemesanan",
    "thread_id": "test-123"
  }'
```

#### 3. Test HITL Status
```bash
curl -X GET "http://localhost:8000/api/v1/hitl/status/test-123"
```

### C. Testing dengan Script

```bash
cd backend
python test_hitl.py
```

## Expected Responses

### 1. HITL Initiate Response (Waiting for Input)
```json
{
  "status": "waiting_for_input",
  "thread_id": "550e8400-e29b-41d4-a716-************",
  "message": "🏨 **Konfirmasi Pemesanan Hotel**\n\n📍 **Hotel**: Hotel Ubud...",
  "waiting_for_human": true,
  "hitl_data": {
    "type": "confirmation",
    "options": ["Ya, lanjutkan", "Tidak, batalkan", "Ubah detail"]
  }
}
```

### 2. HITL Continue Response (Payment Selection)
```json
{
  "status": "waiting_for_input",
  "thread_id": "550e8400-e29b-41d4-a716-************",
  "message": "💳 **Konfirmasi Pembayaran**\n\n💰 **Total**: Rp 500,000...",
  "waiting_for_human": true,
  "hitl_data": {
    "type": "selection",
    "options": ["Transfer Bank", "Kartu Kredit", "E-Wallet"]
  }
}
```

### 3. HITL Final Response (Success)
```json
{
  "status": "success",
  "thread_id": "550e8400-e29b-41d4-a716-************",
  "message": "✅ **Pembayaran Hotel Berhasil!**\n\n🎫 **ID Pemesanan**: 123...",
  "waiting_for_human": false,
  "hitl_data": null
}
```

## Troubleshooting

### 1. HITL Tidak Terpicu
**Problem:** Pesan tidak memicu HITL
**Solution:** 
- Pastikan pesan mengandung kata kunci booking + hotel
- Contoh: "pesan hotel", "book hotel", "reservasi hotel"

### 2. Tombol Tidak Muncul di Telegram
**Problem:** UI konfirmasi tidak muncul
**Solution:**
- Cek log Telegram bot
- Pastikan `hitl_handler` terinisialisasi
- Cek callback query handler

### 3. API Error 500
**Problem:** Internal server error
**Solution:**
- Cek log backend untuk error details
- Pastikan database connection
- Cek import modules

### 4. Session Tidak Tersimpan
**Problem:** HITL session hilang
**Solution:**
- Cek `active_sessions` di HITLHandler
- Pastikan thread_id konsisten
- Cek timeout session

## Monitoring

### Logs yang Perlu Diperhatikan

**Backend Logs:**
```
HITL: Requesting booking confirmation from user
HITL: Received user response: Ya, lanjutkan pemesanan
HITL Hotel Booking: User confirmed, proceeding with booking
```

**Telegram Bot Logs:**
```
HITL: Initiating conversation for user 123456
HITL: Handling callback hitl_confirm_yes for user 123456
HITL: Continuing conversation for user 123456 with response: Ya, lanjutkan pemesanan
```

### Metrics

Monitor metrics berikut:
- `telegram_api_calls_total{status="success"}`
- `telegram_response_time_seconds{message_type="hitl_api"}`
- `agent_invocations_total{agent_type="hotel_booking_confirmation"}`

## Next Steps

1. **Extend to Flight Booking:** Implementasi HITL untuk booking pesawat
2. **Tour Booking:** Implementasi HITL untuk booking tour
3. **Cancellation Flow:** HITL untuk pembatalan booking
4. **Advanced Validation:** Validasi input yang lebih kompleks
5. **Multi-step Booking:** HITL untuk booking multi-step

## Support

Jika mengalami masalah:
1. Cek logs backend dan telegram bot
2. Test dengan script `test_hitl.py`
3. Verifikasi API endpoints dengan curl
4. Cek database untuk data booking
